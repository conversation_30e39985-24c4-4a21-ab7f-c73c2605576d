import React, { useState, useRef } from 'react';
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-components';
import { Tag, Button, Modal, message } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { wxComponentGetUserAuthRecords } from '@/services/swagger/wxComponent';
import { loginGetCurrentUser } from '@/services/swagger/login';

interface UserAuthRecordsProps {
  visible: boolean;
  onClose: () => void;
}

const UserAuthRecords: React.FC<UserAuthRecordsProps> = ({ visible, onClose }) => {
  const [currentUser, setCurrentUser] = useState<API.UserPublic | null>(null);
  const actionRef = useRef<ActionType>();

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    try {
      const res = await loginGetCurrentUser();
      setCurrentUser(res);
      return res;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  };

  const columns: ProColumns<API.WxUserAuthRecord>[] = [
    {
      title: '公众号AppID',
      dataIndex: 'appid',
      key: 'appid',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '公众号名称',
      dataIndex: 'nick_name',
      key: 'nick_name',
      ellipsis: true,
    },
    {
      title: '授权时间',
      dataIndex: 'auth_time',
      key: 'auth_time',
      valueType: 'dateTime',
      sorter: true,
    },
    {
      title: '授权状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => (
        <Tag color={record.status === 'active' ? 'success' : 'error'}>
          {record.status === 'active' ? '有效' : '无效'}
        </Tag>
      ),
    },
    {
      title: '会话ID',
      dataIndex: 'auth_session_id',
      key: 'auth_session_id',
      ellipsis: true,
      copyable: true,
    },
  ];

  return (
    <Modal
      title="我的授权记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <ProTable<API.WxUserAuthRecord>
        headerTitle="授权记录列表"
        actionRef={actionRef}
        rowKey="id"
        search={false}
        request={async (params) => {
          // 获取当前用户信息
          const user = currentUser || await fetchCurrentUser();
          if (!user?.username) {
            message.error('用户信息获取失败');
            return {
              data: [],
              success: false,
            };
          }

          try {
            const res = await wxComponentGetUserAuthRecords({
              user_id: user.username,
              ...params,
            });
            return {
              data: res.data || [],
              success: res.succeed,
            };
          } catch (error) {
            console.error('获取授权记录失败:', error);
            return {
              data: [],
              success: false,
            };
          }
        }}
        columns={columns}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </Modal>
  );
};

export default UserAuthRecords;

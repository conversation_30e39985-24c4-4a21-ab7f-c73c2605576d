import React, { useEffect, useState } from 'react';
import { Card, Result, Spin, message } from 'antd';
import { useSearchParams, history } from '@umijs/max';
import { wxComponentHandleAuthCallback } from '@/services/swagger/wxComponent';

const AuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState<'success' | 'error' | 'processing'>('processing');
  const [resultMessage, setResultMessage] = useState('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const authCode = searchParams.get('auth_code');
        const expiresIn = searchParams.get('expires_in');
        const authSessionId = localStorage.getItem('auth_session_id');

        if (!authCode || !expiresIn || !authSessionId) {
          setStatus('error');
          setResultMessage('授权参数不完整');
          setLoading(false);
          return;
        }

        const callbackRequest: API.WxAuthCallbackRequest = {
          auth_code: authCode,
          expires_in: parseInt(expiresIn),
          auth_session_id: authSessionId,
        };

        const res = await wxComponentHandleAuthCallback(callbackRequest);
        
        if (res.succeed && res.data?.success) {
          setStatus('success');
          setResultMessage('授权成功！');
          message.success('公众号授权成功');
          
          // 清除本地存储的授权会话ID
          localStorage.removeItem('auth_session_id');
          
          // 3秒后跳转回公众号管理页面
          setTimeout(() => {
            history.push('/account/setting');
          }, 3000);
        } else {
          setStatus('error');
          setResultMessage(res.errorMessage || '授权处理失败');
        }
      } catch (error) {
        console.error('处理授权回调失败:', error);
        setStatus('error');
        setResultMessage('授权处理失败，请重试');
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [searchParams]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Card>
          <Spin size="large" />
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            正在处理授权信息...
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '400px' 
    }}>
      <Card style={{ width: 500 }}>
        <Result
          status={status === 'success' ? 'success' : 'error'}
          title={status === 'success' ? '授权成功' : '授权失败'}
          subTitle={resultMessage}
          extra={
            status === 'success' ? (
              <div style={{ textAlign: 'center', color: '#666' }}>
                3秒后自动跳转到公众号管理页面...
              </div>
            ) : null
          }
        />
      </Card>
    </div>
  );
};

export default AuthCallback;

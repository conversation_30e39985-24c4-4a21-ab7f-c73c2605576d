import React, { useState, useRef, useEffect } from 'react';
import {
  Button,
  message,
  List,
  Spin,
  Select,
  Modal,
  Empty,
  Tag,
  Form,
  Steps,
  Space,
  Tooltip,
  Card,
  Cascader,
} from 'antd';
import { FileOutlined, FileTextOutlined, FileMarkdownOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { RadioChangeEvent } from 'antd';
import { articleUploadArticles } from '@/services/swagger/article';
import { imageGetArticleThumbs, imageAddThumbImage } from '@/services/swagger/image';
import { officialAccountGetOfficialAccountList } from '@/services/swagger/officialAccount';
import { MdEditor } from 'md-editor-rt';
import 'md-editor-rt/lib/style.css';
import { Splitter } from 'antd';
import { categoryGetAllArticleClassifications } from '@/services/swagger/category';
import ThumbSelector from '@/components/ThumbSelector';

// 定义文件类型
interface FileItem {
  path: string;
  name: string;
  content: string;
  type: 'html' | 'text' | 'markdown';
  convertedContent?: string;
}

// 定义缩略图类型（与API.ArticleThumbTable保持一致）
interface ThumbItem {
  id?: string;
  thumb_media_id: string;
  url?: string | null;
  local_path?: string | null;
  title?: string | null;
  create_at?: string;
  update_at?: string;
  deleted_at?: string | null;
  is_delete?: boolean;
  category?: string | null;
  category_id?: string | null;
  x1?: number | null;
  y1?: number | null;
  x2?: number | null;
  y2?: number | null;
  user_id?: string | null;
}

// 定义公众号类型
interface OfficialAccount {
  id: string;
  appid?: string | null;
  nick_name?: string | null;
}

// 定义分类类型
interface ClassifyOption {
  label: string;
  value: string;
  children?: ClassifyOption[];
}

const UploadArticlePage: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [thumbMode, setThumbMode] = useState<'select' | 'upload'>('select');
  const [thumbList, setThumbList] = useState<ThumbItem[]>([]);
  const [selectedThumb, setSelectedThumb] = useState<string | undefined>(undefined);
  const [officialAccounts, setOfficialAccounts] = useState<OfficialAccount[]>([]);
  const [selectedOfficialAccount, setSelectedOfficialAccount] = useState<string>('');
  const [previewTheme, setPreviewTheme] = useState<string>('cyanosis');
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [processingFiles, setProcessingFiles] = useState<boolean>(false);
  const [activeTabKey, setActiveTabKey] = useState<string>('preview');
  const [htmlBlobUrl, setHtmlBlobUrl] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [classifyOptions, setClassifyOptions] = useState<ClassifyOption[]>([]);
  const [selectedClassify, setSelectedClassify] = useState<string[]>([]);
  const [articleClassifyOptions, setArticleClassifyOptions] = useState<ClassifyOption[]>([]);
  const [selectedArticleClassify, setSelectedArticleClassify] = useState<string[]>([]);

  const directoryInputRef = useRef<HTMLInputElement>(null);

  // 预览主题选项
  const previewThemeOptions = [
    { label: "default", value: "default" },
    { label: "github", value: "github" },
    { label: "vuepress", value: "vuepress" },
    { label: "mk-cute", value: "mk-cute" },
    { label: "smart-blue", value: "smart-blue" },
    { label: "cyanosis", value: "cyanosis" },
  ];

  // 组件加载时获取缩略图和公众号列表
  useEffect(() => {
    fetchThumbList();
    fetchOfficialAccounts();
    fetchClassifyOptions();
    fetchArticleClassifyOptions();
  }, []);

  // 当选择的文件变化时，如果是HTML文件，创建一个新的Blob URL
  useEffect(() => {
    // 先释放之前的URL
    if (htmlBlobUrl) {
      URL.revokeObjectURL(htmlBlobUrl);
      setHtmlBlobUrl(null);
    }

    // 如果是HTML文件，创建新的Blob URL
    if (selectedFile && selectedFile.type === 'html') {
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <title>${selectedFile.name}</title>
            <style>
              body {
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
            </style>
            <base target="_blank">
          </head>
          <body>
            ${selectedFile.content}
          </body>
        </html>
      `;

      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      setHtmlBlobUrl(url);
    }

    // 组件卸载时清理URL
    return () => {
      if (htmlBlobUrl) {
        URL.revokeObjectURL(htmlBlobUrl);
      }
    };
  }, [selectedFile]);

  // 获取缩略图列表
  const fetchThumbList = async () => {
    try {
      setLoading(true);
      const res = await imageGetArticleThumbs({ current: 1, pageSize: 50 });
      if (res.succeed && res.data?.items) {
        setThumbList(res.data.items as any[]);
      } else {
        message.error('获取缩略图列表失败');
      }
    } catch (error) {
      console.error('获取缩略图列表失败:', error);
      message.error('获取缩略图列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取公众号列表
  const fetchOfficialAccounts = async () => {
    try {
      const res = await officialAccountGetOfficialAccountList();
      if (res.succeed && res.data) {
        setOfficialAccounts(res.data);
        if (res.data.length > 0 && res.data[0].appid) {
          setSelectedOfficialAccount(res.data[0].appid || '');
        }
      }
    } catch (error) {
      console.error('获取公众号列表失败:', error);
      message.error('获取公众号列表失败');
    }
  };

  // 获取分类列表
  const fetchClassifyOptions = async () => {
    try {
      const res = await categoryGetAllArticleClassifications({ parent_only: false });
      if (res.succeed && res.data) {
        const parentMap = new Map();

        // 创建父级分类映射
        res.data.forEach(item => {
          if (!item.parent) {
            parentMap.set(item.id, {
              label: item.name,
              value: item.id || '',
              children: []
            });
          }
        });

        // 添加子分类到对应的父分类
        res.data.forEach(item => {
          if (item.parent && item.parent.id && parentMap.has(item.parent.id)) {
            parentMap.get(item.parent.id).children.push({
              label: item.name,
              value: item.id || ''
            });
          }
        });

        setClassifyOptions(Array.from(parentMap.values()));
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
      message.error('获取分类列表失败');
    }
  };

  // 获取文章分类列表
  const fetchArticleClassifyOptions = async () => {
    try {
      const res = await categoryGetAllArticleClassifications({ parent_only: false });
      if (res.succeed && res.data) {
        const parentMap = new Map();

        // 创建父级分类映射
        res.data.forEach(item => {
          if (!item.parent) {
            parentMap.set(item.id, {
              label: item.name,
              value: item.id || '',
              children: []
            });
          }
        });

        // 添加子分类到对应的父分类
        res.data.forEach(item => {
          if (item.parent && item.parent.id && parentMap.has(item.parent.id)) {
            parentMap.get(item.parent.id).children.push({
              label: item.name,
              value: item.id || ''
            });
          }
        });

        setArticleClassifyOptions(Array.from(parentMap.values()));
      }
    } catch (error) {
      console.error('获取文章分类列表失败:', error);
      message.error('获取文章分类列表失败');
    }
  };

  // 处理分类选择变化
  const handleClassifyChange = (value: string[]) => {
    setSelectedClassify(value);
  };

  // 处理文章分类选择变化
  const handleArticleClassifyChange = (value: string[]) => {
    setSelectedArticleClassify(value);
  };

  // 处理公众号选择变化
  const handleOfficialAccountChange = (value: string) => {
    setSelectedOfficialAccount(value);

    // 自动将文章分类初始化为和公众号一个分类
    const selectedAccount = officialAccounts.find(account => account.appid === value);
    if (selectedAccount && (selectedAccount as any).category) {
      const categoryId = (selectedAccount as any).category.id;
      if (categoryId) {
        // 需要构建完整的分类路径数组
        const category = (selectedAccount as any).category;
        if (category.parent_id) {
          // 如果是子分类，需要找到父分类
          const parentCategory = articleClassifyOptions.find(option =>
            option.children?.some((child: any) => child.value === categoryId)
          );
          if (parentCategory) {
            setSelectedArticleClassify([parentCategory.value, categoryId]);
          } else {
            setSelectedArticleClassify([categoryId]);
          }
        } else {
          // 如果是父分类
          setSelectedArticleClassify([categoryId]);
        }
      }
    } else {
      // 如果公众号没有分类，清空分类选择
      setSelectedArticleClassify([]);
    }
  };

  // 处理文件夹选择
  const handleDirectorySelect = async () => {
    if (!directoryInputRef.current) return;
    directoryInputRef.current.click();
  };

  // 读取文件内容
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('读取文件失败'));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsText(file);
    });
  };

  // 处理文件夹选择变更
  const handleDirectoryChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setProcessingFiles(true);

    try {
      const fileItems: FileItem[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileName = file.name.toLowerCase();

        // 仅处理 HTML, TXT 和 Markdown 文件
        if (fileName.endsWith('.html') || fileName.endsWith('.txt') ||
            fileName.endsWith('.md') || fileName.endsWith('.markdown') ||
            fileName.endsWith('.htm')) {

          const content = await readFileContent(file);
          let fileType: 'html' | 'text' | 'markdown' = 'text';

          if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
            fileType = 'html';
          } else if (fileName.endsWith('.md') || fileName.endsWith('.markdown')) {
            fileType = 'markdown';
          }

          fileItems.push({
            path: file.webkitRelativePath || file.name,
            name: file.name,
            content,
            type: fileType
          });
        }
      }

      setFileList(fileItems);
      if (fileItems.length > 0) {
        setSelectedFile(fileItems[0]);
      }

      message.success(`成功读取 ${fileItems.length} 个文件`);
    } catch (error) {
      console.error('处理文件失败:', error);
      message.error('处理文件失败');
    } finally {
      setProcessingFiles(false);
    }
  };

  // 更改选中的文件
  const handleFileSelect = (file: FileItem) => {
    setSelectedFile(file);
  };

  // 更改缩略图模式
  const handleThumbModeChange = (e: RadioChangeEvent) => {
    setThumbMode(e.target.value);
  };

  // 选择缩略图
  const handleThumbSelect = (thumbId: string) => {
    setSelectedThumb(thumbId);
  };

  // 上传缩略图到微信
  const handleThumbUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    if (!selectedOfficialAccount) {
      message.error('请先选择公众号');
      onError(new Error('请先选择公众号'));
      return;
    }

    try {
      // 构建FormData对象
      const formData = new FormData();
      formData.append('media', file);

      // 请求微信API上传素材
      // 注意：实际上可能需要通过服务端代理来解决跨域问题
      const response = await fetch(`https://wxcomponent-290797-160013-8-**********.sh.run.tcloudbase.com/wxcomponent/official-account/material/uploadImage?appid=${selectedOfficialAccount}`, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      console.log(result)

      if (result.media_id) {
        // 上传成功，调用后端API保存记录
        const thumbData:API.ArticleThumbCreate = {
          thumb_media_id: result.media_id,
          url: result.url,
          title: file.name,
          category_id: selectedClassify && selectedClassify.length > 0 ? selectedClassify[selectedClassify.length - 1] : undefined,
          official_account_id:selectedOfficialAccount
        };

        const saveResult = await imageAddThumbImage(thumbData);

        if (saveResult.succeed) {
          message.success('上传缩略图成功');
          onSuccess(result);
          fetchThumbList(); // 刷新缩略图列表
          setSelectedThumb(result.media_id);
        } else {
          message.error('保存缩略图记录失败');
          onError(new Error('保存缩略图记录失败'));
        }
      } else {
        message.error('上传缩略图失败: ' + (result.errmsg || '未知错误'));
        onError(new Error('上传缩略图失败'));
      }
    } catch (error) {
      console.error('上传缩略图出错:', error);
      message.error('上传缩略图失败');
      onError(error);
    }
  };

  // 更新文件的转换后内容
  const updateFileConvertedContent = (content: string) => {
    if (!selectedFile) return;

    setFileList(prev =>
      prev.map(file =>
        file.path === selectedFile.path
          ? { ...file, convertedContent: content }
          : file
      )
    );

    setSelectedFile(prev =>
      prev ? { ...prev, convertedContent: content } : null
    );
  };

  // 将Markdown转换为HTML
  const convertMarkdownToHtml = (markdown: string, theme: string) => {
    // 使用md-editor-rt的预览功能来生成HTML
    // 这里需要创建一个临时容器来获取渲染结果
    const tempDiv = document.createElement('div');
    tempDiv.style.display = 'none';
    document.body.appendChild(tempDiv);

    try {
      // 给容器添加特定主题的类名
      tempDiv.className = `md-editor-preview md-editor-preview-${theme}`;

      // 使用md-editor-rt的预览功能来生成HTML
      // 这里可以使用第三方库如markdown-it进行转换
      // 为了简单起见，我们使用一个简单的转换函数
      const htmlContent = convertMarkdownContent(markdown);
      tempDiv.innerHTML = htmlContent;

      // 获取HTML内容
      const fullHtml = tempDiv.innerHTML;

      // 返回完整的HTML文档
      return `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <title>Generated from Markdown</title>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/md-editor-rt@2.8.1/lib/style.css">
            <style>
              body {
                max-width: 900px;
                margin: 0 auto;
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
              img {
                max-width: 100%;
              }
              pre {
                background-color: #f6f8fa;
                border-radius: 3px;
                padding: 16px;
                overflow: auto;
              }
              code {
                font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
              }
              table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 16px;
              }
              table, th, td {
                border: 1px solid #ddd;
              }
              th, td {
                padding: 8px;
                text-align: left;
              }
              th {
                background-color: #f2f2f2;
              }
              blockquote {
                border-left: 4px solid #ddd;
                padding-left: 16px;
                margin-left: 0;
                color: #666;
              }
              /* 添加主题特定样式 */
              .md-editor-preview-${theme} {
                /* 根据主题添加特定样式 */
              }
            </style>
          </head>
          <body class="md-editor-preview md-editor-preview-${theme}">
            ${fullHtml}
          </body>
        </html>
      `;
    } finally {
      // 确保在任何情况下都移除临时元素
      if (tempDiv && tempDiv.parentNode) {
        document.body.removeChild(tempDiv);
      }
    }
  };

  // Markdown转HTML的辅助函数
  const convertMarkdownContent = (markdown: string): string => {
    // 这里使用一个简单的实现，实际项目中应该使用成熟的Markdown解析库
    let html = markdown;

    // 先处理代码块
    html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // 处理行内代码
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // 处理标题
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
    html = html.replace(/^##### (.*$)/gm, '<h5>$1</h5>');
    html = html.replace(/^###### (.*$)/gm, '<h6>$1</h6>');

    // 处理加粗
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // 处理斜体
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 处理链接
    html = html.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');

    // 处理图片
    html = html.replace(/!\[(.*?)\]\((.*?)\)/g, '<img alt="$1" src="$2">');

    // 处理分隔线
    html = html.replace(/^\-\-\-$/gm, '<hr>');

    // 处理无序列表
    html = html.replace(/^\- (.*$)/gm, '<ul><li>$1</li></ul>');
    html = html.replace(/^\* (.*$)/gm, '<ul><li>$1</li></ul>');

    // 处理有序列表
    html = html.replace(/^(\d+)\. (.*$)/gm, '<ol><li>$2</li></ol>');

    // 处理引用
    html = html.replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>');

    // 处理表格 (简化版)
    html = html.replace(/\|(.+)\|/g, '<table><tr><td>$1</td></tr></table>');

    // 处理段落 (确保在最后处理，避免影响其他格式)
    html = html.replace(/^([^<\n].*)/gm, '<p>$1</p>');

    // 清理重复的标签
    html = html.replace(/<\/ul><ul>/g, '');
    html = html.replace(/<\/ol><ol>/g, '');
    html = html.replace(/<\/blockquote><blockquote>/g, '');

    return html;
  };

  // 将当前主题应用到所有Markdown文件
  const applyThemeToAllMarkdownFiles = () => {
    // 创建一个新的文件列表副本
    const newFileList = [...fileList];

    // 遍历并更新所有Markdown文件
    for (let i = 0; i < newFileList.length; i++) {
      const file = newFileList[i];
      if (file.type === 'markdown') {
        // 生成HTML并保存为转换后的内容
        const htmlContent = convertMarkdownToHtml(file.content, previewTheme);
        newFileList[i] = { ...file, convertedContent: htmlContent };
      }
    }

    // 更新状态
    setFileList(newFileList);

    // 如果当前选中的是Markdown文件，也更新它
    if (selectedFile && selectedFile.type === 'markdown') {
      const htmlContent = convertMarkdownToHtml(selectedFile.content, previewTheme);
      setSelectedFile({
        ...selectedFile,
        convertedContent: htmlContent
      });
    }

    message.success(`已将 ${previewTheme} 主题应用到所有 Markdown 文件`);

    // 打印日志以便调试
    console.log('转换后的文件列表:', newFileList.map(f => ({
      name: f.name,
      type: f.type,
      hasConvertedContent: !!f.convertedContent
    })));

    // 自动前进到下一步
    if (currentStep === 1) {
      setCurrentStep(2);
    }
  };

  // 检查是否有Markdown文件
  const hasMarkdownFiles = fileList.some(file => file.type === 'markdown');

  // 检查所有Markdown文件是否都已转换
  const allMarkdownConverted = fileList.every(file =>
    file.type !== 'markdown' || file.convertedContent
  );

  // 渲染文件列表
  const renderFileList = () => {
    if (processingFiles) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip="处理文件中..." />
        </div>
      );
    }

    if (fileList.length === 0) {
      return (
        <Empty description="请选择文件夹" />
      );
    }

    return (
      <List
        dataSource={fileList}
        renderItem={item => (
          <List.Item
            key={item.path}
            onClick={() => handleFileSelect(item)}
            style={{
              cursor: 'pointer',
              backgroundColor: selectedFile && selectedFile.path === item.path ? '#f0f0f0' : 'transparent'
            }}
          >
            <List.Item.Meta
              avatar={
                item.type === 'html'
                  ? <FileTextOutlined />
                  : item.type === 'markdown'
                    ? <FileMarkdownOutlined />
                    : <FileOutlined />
              }
              title={item.name}
              description={item.path}
            />
            <Tag color={
              item.type === 'html'
                ? 'blue'
                : item.type === 'markdown'
                  ? 'green'
                  : 'gray'
            }>
              {item.type === 'html'
                ? 'HTML'
                : item.type === 'markdown'
                  ? 'Markdown'
                  : 'Text'}
            </Tag>
          </List.Item>
        )}
      />
    );
  };

  // 渲染预览内容
  const renderPreview = () => {
    if (!selectedFile) {
      return <Empty description="请选择文件" />;
    }

    if (selectedFile.type === 'markdown') {
      return (
        <div>
          <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>Markdown 预览</span>
            <Space>
              <Select
                options={previewThemeOptions}
                value={previewTheme}
                style={{ width: 150 }}
                onChange={(value) => setPreviewTheme(value)}
              />
              <Tooltip title="将当前主题应用到所有Markdown文件，生成HTML代码用于上传">
                <Button
                  type="primary"
                  size="small"
                  onClick={applyThemeToAllMarkdownFiles}
                >
                  应用主题到所有文件
                </Button>
              </Tooltip>
            </Space>
          </div>
          <MdEditor
            value={selectedFile.content}
            onChange={(content) => {
              // 更新文件内容
              updateFileConvertedContent(content);

              // 同时更新转换后的HTML内容
              if (content) {
                const htmlContent = convertMarkdownToHtml(content, previewTheme);

                // 更新文件列表中当前文件的转换内容
                setFileList(prev =>
                  prev.map(file =>
                    file.path === selectedFile.path
                      ? { ...file, content, convertedContent: htmlContent }
                      : file
                  )
                );

                // 更新选中的文件
                setSelectedFile(prev =>
                  prev ? { ...prev, content, convertedContent: htmlContent } : null
                );
              }
            }}
            previewTheme={previewTheme}
            preview={true}
            readOnly={true}
            style={{ height: '700px' }}
          />
          <div style={{ marginTop: '16px', color: '#888' }}>
            <p>提示: 选择一个喜欢的主题后，点击"应用主题到所有文件"将转换所有Markdown文件为对应主题的HTML代码。</p>
            {selectedFile.convertedContent ? (
              <p style={{ color: 'green' }}>✓ 该文件已转换为HTML，上传时将使用转换后的内容</p>
            ) : (
              <p style={{ color: 'orange' }}>⚠ 该文件尚未转换为HTML，请应用主题进行转换</p>
            )}
          </div>
        </div>
      );
    }

    if (selectedFile.type === 'html') {
      return (
        <div>
          <div style={{ marginBottom: '16px' }}>
            {selectedFile.name}
          </div>
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            height: '700px',
            overflow: 'hidden'
          }}>
            {htmlBlobUrl && (
              <iframe
                src={htmlBlobUrl}
                title={selectedFile.name}
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none'
                }}
                sandbox="allow-same-origin"
              />
            )}
          </div>
        </div>
      );
    }

    // 纯文本内容
    return (
      <div>
        <div style={{ marginBottom: '16px' }}>
          {selectedFile.name}
        </div>
        <div style={{
          border: '1px solid #d9d9d9',
          padding: '16px',
          borderRadius: '4px',
          height: '700px',
          overflow: 'auto'
        }}>
          <pre>{selectedFile.content}</pre>
        </div>
      </div>
    );
  };

  // 渲染封面图选择
  const renderThumbSettings = () => {
    return (
      <div>
        <Form layout="vertical">
          <Form.Item
            label={
              <span>
                公众号选择
                <Tooltip title="选择要上传文章的目标公众号">
                  <QuestionCircleOutlined style={{ marginLeft: '4px' }} />
                </Tooltip>
              </span>
            }
          >
            <Select
              style={{ width: '100%' }}
              placeholder="请选择公众号"
              value={selectedOfficialAccount}
              onChange={(value) => setSelectedOfficialAccount(value)}
              options={officialAccounts.map(item => ({
                label: item.nick_name || item.appid || '未命名',
                value: item.appid || ''
              }))}
            />
          </Form.Item>

          <Form.Item
            label={
              <span>
                文章分类
                <Tooltip title="为文章选择一个分类，便于管理">
                  <QuestionCircleOutlined style={{ marginLeft: '4px' }} />
                </Tooltip>
              </span>
            }
          >
            <Cascader
              options={articleClassifyOptions}
              expandTrigger="hover"
              displayRender={(labels) => labels.join(' / ')}
              placeholder="请选择文章分类"
              onChange={(value) => handleArticleClassifyChange(value as string[])}
              style={{ width: '100%' }}
              allowClear
            />
          </Form.Item>

          <Form.Item
            label={
              <span>
                封面图
                <Tooltip title="为文章选择或上传一个封面图">
                  <QuestionCircleOutlined style={{ marginLeft: '4px' }} />
                </Tooltip>
              </span>
            }
          >
            <ThumbSelector
              value={selectedThumb}
              onChange={handleThumbSelect}
              officialAccountId={selectedOfficialAccount}
              categoryId={selectedClassify && selectedClassify.length > 0 ? selectedClassify[selectedClassify.length - 1] : undefined}
            />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // 处理文章上传
  const handleUploadArticles = async () => {
    try {
      if (!selectedOfficialAccount) {
        message.error('请先选择公众号');
        return;
      }

      if (!selectedThumb) {
        message.error('请选择封面图');
        return;
      }

      if (fileList.length === 0) {
        message.error('请先选择要上传的文件');
        return;
      }

      const articlesToUpload: API.ArticleCreateByUpload[] = fileList.map(file => {
        // 优先使用转换后的内容（用于Markdown）
        const content = file.convertedContent || file.content;
        return {
          title: file.name,
          content: content,
          thumb_media_id: selectedThumb,
          category_id: selectedArticleClassify && selectedArticleClassify.length > 0 ?
            selectedArticleClassify[selectedArticleClassify.length - 1] : undefined,
          source: '用户上传'
        };
      });

      setProcessingFiles(true);
      const res = await articleUploadArticles(articlesToUpload);
      setProcessingFiles(false);

      if (res.succeed) {
        message.success('上传成功，共上传 ' + res.data?.length + ' 篇文章');
        setFileList([]);
        setSelectedFile(null);
        setCurrentStep(0);
      } else {
        message.error('上传失败: ' + res.errorMessage);
      }
    } catch (error) {
      console.error('上传出错:', error);
      message.error('上传出错');
      setProcessingFiles(false);
    }
  };

  return (
    <>
      <div style={{ marginBottom: '16px' }}>
        <h2>上传文章</h2>
        <Steps
          current={currentStep}
          onChange={setCurrentStep}
          items={[
            {
              title: '选择文件',
              description: '选择包含文章的文件夹',
              status: fileList.length > 0 ? 'finish' : 'process',
            },
            {
              title: '设置Markdown主题',
              description: '选择并应用Markdown主题',
              status: allMarkdownConverted || !hasMarkdownFiles ? 'finish' : 'wait',
              disabled: fileList.length === 0,
            },
            {
              title: '设置封面图',
              description: '选择或上传文章封面图',
              status: selectedThumb ? 'finish' : 'wait',
              disabled: fileList.length === 0,
            },
          ]}
        />
      </div>

      <Splitter style={{ height: 'calc(100vh - 240px)', boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)' }}>
        {/* 左侧：文件列表 */}
        <Splitter.Panel defaultSize="30%" min="20%" max="40%">
          <Card
            title={
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                <span>文件列表</span>
                <Button
                  type="primary"
                  onClick={handleDirectorySelect}
                  disabled={processingFiles}
                  size="small"
                >
                  选择文件夹
                </Button>
                <input
                  type="file"
                  ref={directoryInputRef}
                  onChange={handleDirectoryChange}
                  style={{ display: 'none' }}
                  {...{ webkitdirectory: "", directory: "" } as any}
                />
              </div>
            }
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, overflow: 'auto', padding: '8px' }}
            extra={
              <Tooltip title="先选择一个包含文章的文件夹，支持HTML、Markdown和TXT格式">
                <QuestionCircleOutlined />
              </Tooltip>
            }
          >
            {renderFileList()}
          </Card>
        </Splitter.Panel>

        {/* 右侧：预览和封面图设置 */}
        <Splitter.Panel>
          <Card
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, overflow: 'auto', padding: '16px' }}
            tabList={[
              { key: 'preview', tab: '文件预览' },
              { key: 'thumb', tab: '封面图设置' }
            ]}
            activeTabKey={activeTabKey}
            onTabChange={setActiveTabKey}
            tabBarExtraContent={
              currentStep === 1 && hasMarkdownFiles ? (
                <Tooltip title="第二步: 选择一个喜欢的Markdown主题，然后应用到所有文件">
                  <QuestionCircleOutlined style={{ marginRight: '8px' }} />
                </Tooltip>
              ) : currentStep === 2 ? (
                <Tooltip title="第三步: 选择一个封面图作为文章缩略图">
                  <QuestionCircleOutlined style={{ marginRight: '8px' }} />
                </Tooltip>
              ) : null
            }
          >
            {activeTabKey === 'preview' ? renderPreview() : renderThumbSettings()}
          </Card>
        </Splitter.Panel>
      </Splitter>

      <div style={{ marginTop: '20px', textAlign: 'center' }}>
        <Button
          type="primary"
          size="large"
          onClick={handleUploadArticles}
          disabled={loading || fileList.length === 0 || !selectedThumb}
          style={{ width: '200px', height: '45px' }}
        >
          上传所有文章
        </Button>
        {!selectedThumb && fileList.length > 0 && (
          <div style={{ marginTop: '8px', color: '#ff4d4f' }}>
            请先选择封面图后再上传文章
          </div>
        )}
        {hasMarkdownFiles && !allMarkdownConverted && (
          <div style={{ marginTop: '8px', color: '#faad14' }}>
            有Markdown文件尚未转换为HTML，上传前请先应用主题
          </div>
        )}
        {fileList.length > 0 && selectedArticleClassify.length === 0 && (
          <div style={{ marginTop: '8px', color: '#faad14' }}>
            建议为文章选择一个分类，便于后续管理
          </div>
        )}
      </div>

      <Modal
        title="上传文章"
        open={uploadModalVisible}
        footer={null}
        closable={false}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip="上传文章中..." />
        </div>
      </Modal>
    </>
  );
};

export default UploadArticlePage;

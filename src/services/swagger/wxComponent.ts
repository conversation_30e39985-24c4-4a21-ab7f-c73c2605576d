// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取预授权码 GET /api/v1/wx_component/pre_auth_code */
export async function wxComponentGetPreAuthCode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxComponentGetPreAuthCodeParams,
  options?: { [key: string]: any },
) {
  return request<API.ResponseBodyWxPreAuthCodeResponse_>('/api/v1/wx_component/pre_auth_code', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 生成授权链接 POST /api/v1/wx_component/auth_url */
export async function wxComponentGenerateAuthUrl(
  body: API.WxAuthUrlRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResponseBodyWxAuthUrlResponse_>('/api/v1/wx_component/auth_url', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取授权状态 GET /api/v1/wx_component/auth_status */
export async function wxComponentGetAuthStatus(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxComponentGetAuthStatusParams,
  options?: { [key: string]: any },
) {
  return request<API.ResponseBodyWxAuthStatusResponse_>('/api/v1/wx_component/auth_status', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 处理授权回调 POST /api/v1/wx_component/auth_callback */
export async function wxComponentHandleAuthCallback(
  body: API.WxAuthCallbackRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResponseBodyWxAuthCallbackResponse_>('/api/v1/wx_component/auth_callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户授权记录 GET /api/v1/wx_component/user_auth_records */
export async function wxComponentGetUserAuthRecords(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxComponentGetUserAuthRecordsParams,
  options?: { [key: string]: any },
) {
  return request<API.ResponseBodyListWxUserAuthRecord_>('/api/v1/wx_component/user_auth_records', {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',
      // pageSize has a default value: 50
      pageSize: '50',
      ...params,
    },
    ...(options || {}),
  });
}

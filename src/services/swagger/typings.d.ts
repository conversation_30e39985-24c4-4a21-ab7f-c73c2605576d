declare namespace API {
  type ApiDefaultSetting = {
    /** Generate Image */
    generate_image?: boolean | null;
    generate_image_platform?: ImagePlatform | null;
    /** Generate Image Nums */
    generate_image_nums?: number | null;
    platform?: ThirdApiPlatform | null;
    /** Model */
    model?: string | null;
    /** Ref Platform */
    ref_platform?: string | null;
    /** Ref Num */
    ref_num?: number | null;
    /** Promote */
    promote?: string | null;
    /** Id */
    id: string;
  };

  type ApiDefaultSettingIn = {
    /** Generate Image */
    generate_image?: boolean | null;
    generate_image_platform?: ImagePlatform | null;
    /** Generate Image Nums */
    generate_image_nums?: number | null;
    platform?: ThirdApiPlatform | null;
    /** Model */
    model?: string | null;
    /** Ref Platform */
    ref_platform?: string | null;
    /** Ref Num */
    ref_num?: number | null;
    /** Promote */
    promote?: string | null;
  };

  type ArticleCategoryCreate = {
    /** Name */
    name: string;
    /** Description */
    description?: string | null;
    /** Parent Id */
    parent_id?: string | null;
  };

  type ArticleCategoryPublic = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Name */
    name: string;
    /** Description */
    description?: string | null;
    parent?: ArticleCategoryTable | null;
  };

  type ArticleCategoryTable = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Name */
    name: string;
    /** Description */
    description?: string | null;
    /** Parent Id */
    parent_id?: string | null;
    /** User Id */
    user_id?: string;
  };

  type ArticleCategoryUpdate = {
    /** Name */
    name?: string | null;
    /** Description */
    description?: string | null;
  };

  type ArticleCreate = {
    article: BaseArticleCreate;
    outline?: BaseArticleCreate;
    /** Article Generate Outline First */
    article_generate_outline_first?: boolean;
    /** Article Ref Platform */
    article_ref_platform?: string;
    /** Article Ref Num */
    article_ref_num?: number;
    /** Title */
    title: string[];
    /** Generate Image */
    generate_image?: boolean;
    images?: ImageTaskIn;
    /** Source */
    source?: string;
  };

  type ArticleCreateByOpenAiApi = {
    /** Promote */
    promote: string;
    platform: ThirdApiPlatform;
    /** Model */
    model: string;
    /** Generate Image */
    generate_image?: boolean | null;
    images?: ImageTaskIn | null;
    /** Ref Platform */
    ref_platform?: string | null;
    /** Ref Num */
    ref_num?: number;
    /** Title */
    title: string[];
  };

  type ArticleCreateByUpload = {
    /** Title */
    title: string;
    /** Content */
    content: string;
    /** Thumb Media Id */
    thumb_media_id?: string | null;
    /** Category Id */
    category_id?: string | null;
    /** Source */
    source?: string;
  };

  type articleDeletePoeArticleParams = {
    article_id: string;
  };

  type articleGetAllArticleParams = {
    current?: number;
    pageSize?: number;
  };

  type articleGetArticlesSummaryParams = {
    platform?: ThirdApiPlatform;
    model?: string;
    poe_client?: string;
    poe_session?: string;
    article_status?: TaskStatusEnum;
    ref_status?: TaskStatusEnum;
    image_status?: TaskStatusEnum;
    outline_status?: TaskStatusEnum;
    startTime?: string;
    endTime?: string;
    category_id?: string;
    source?: string;
    sync_to_draft?: boolean;
    current?: number;
    pageSize?: number;
  };

  type articleGetPoeArticleByIdParams = {
    article_id: string;
  };

  type ArticleImitate = {
    /** Title */
    title: string | null;
    /** Content */
    content: string | null;
    /** Url */
    url: string | null;
  };

  type ArticlePublic = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Title */
    title: string;
    /** Origin Content */
    origin_content?: string | null;
    /** Promote */
    promote?: string | null;
    /** Msg Price */
    msg_price?: number;
    /** Formated Content */
    formated_content: string | null;
    /** Wechat Id */
    wechat_id: string | null;
    /** Article Task Id */
    article_task_id: string | null;
    /** Image Task Id */
    image_task_id: string | null;
    session?: PoeSessionPublic | null;
    third_api_key: ThirdApiKeyPublic | null;
    /** Images */
    images?: string[] | null;
    article_status?: TaskStatusEnum | null;
    ref_status?: TaskStatusEnum | null;
    image_status?: TaskStatusEnum | null;
    outline_status?: TaskStatusEnum | null;
    sync_to_draft_status?: TaskStatusEnum | null;
    /** Has Published */
    has_published: boolean;
    /** Sync To Draft */
    sync_to_draft: boolean | null;
    /** Official Account Id */
    official_account_id: string | null;
    official_account: OfficialAccountTable | null;
    category: ArticleCategoryTable | null;
  };

  type ArticleSummaryInfo = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Title */
    title?: string | null;
    session: PoeSessionPublic | null;
    third_api_key: ThirdApiKeyPublic | null;
    /** Api Model */
    api_model?: string | null;
    /** Article Task Id */
    article_task_id: string | null;
    /** Image Task Id */
    image_task_id: string | null;
    /** Msg Price */
    msg_price?: number;
    article_status?: TaskStatusEnum | null;
    ref_status?: TaskStatusEnum | null;
    image_status?: TaskStatusEnum | null;
    outline_status?: TaskStatusEnum | null;
    sync_to_draft_status?: TaskStatusEnum | null;
    /** Images */
    images: string[] | null;
    /** Source */
    source: string | null;
    /** Wechat Id */
    wechat_id: string | null;
    /** Has Published */
    has_published: boolean | null;
    /** Sync To Draft */
    sync_to_draft: boolean | null;
    /** Official Account Id */
    official_account_id: string | null;
    official_account: OfficialAccountTable | null;
    category: ArticleCategoryTable | null;
  };

  type ArticleThumbCreate = {
    /** Thumb Media Id */
    thumb_media_id: string;
    /** Official Account Id */
    official_account_id: string;
    /** Url */
    url?: string | null;
    /** Title */
    title?: string | null;
    /** Category Id */
    category_id?: string | null;
  };

  type ArticleThumbTable = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Thumb Media Id */
    thumb_media_id: string;
    /** Url */
    url?: string | null;
    /** Title */
    title?: string | null;
    /** Local Path */
    local_path?: string | null;
    /** X1 */
    x1?: number | null;
    /** Y1 */
    y1?: number | null;
    /** X2 */
    x2?: number | null;
    /** Y2 */
    y2?: number | null;
    /** User Id */
    user_id?: string | null;
    /** Category Id */
    category_id?: string | null;
    /** Official Account Id */
    official_account_id?: string | null;
  };

  type ArticleUpdate = {
    /** Title */
    title?: string | null;
    /** Formated Content */
    formated_content?: string | null;
    /** Wechat Id */
    wechat_id?: string | null;
    /** Has Published */
    has_published?: boolean | null;
  };

  type articleUpdateArticleParams = {
    article_id: string;
  };

  type BaseArticleCreate = {
    /** Promote */
    promote: string;
    /** Client Id */
    client_id: string;
    /** Session Id */
    session_id?: string | null;
    /** Bot */
    bot?: string;
  };

  type BodyLoginLoginAccessToken = {
    /** Grant Type */
    grant_type?: string | null;
    /** Username */
    username: string;
    /** Password */
    password: string;
    /** Scope */
    scope?: string;
    /** Client Id */
    client_id?: string | null;
    /** Client Secret */
    client_secret?: string | null;
  };

  type categoryDeleteArticleCategoryParams = {
    category_id: string;
  };

  type categoryGetAllArticleClassificationsParams = {
    parent_only?: boolean;
  };

  type categoryGetChildrenByParentIdParams = {
    parent_id: string;
  };

  type categoryUpdateArticleCategoryParams = {
    category_id: string;
  };

  type DeleteResponse = {
    /** Succeed */
    succeed: boolean;
    /** Message */
    message?: string | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type HTTPValidationError = {
    /** Detail */
    detail?: ValidationError[];
  };

  type imageGetArticleThumbsParams = {
    category_id?: string;
    current?: number;
    pageSize?: number;
  };

  type ImagePlatform = '花瓣';

  type ImagePlatformOption = {
    /** Label */
    label: string;
    /** Value */
    value: string;
  };

  type ImageTaskIn = {
    /** Nums */
    nums: number;
    platform: ImagePlatform;
    /** Keyword */
    keyword: string;
  };

  type ImitateArticleCreate = {
    /** Promote */
    promote: string;
    platform: ThirdApiPlatform;
    /** Model */
    model: string;
    /** Generate Image */
    generate_image?: boolean | null;
    images?: ImageTaskIn | null;
    /** Ref Platform */
    ref_platform?: string | null;
    /** Ref Num */
    ref_num?: number;
    /** Article Imitate */
    article_imitate: ArticleImitate[];
  };

  type loginRegisterUserParams = {
    username: string;
    is_admin?: boolean;
    is_active?: boolean;
    avatar: string;
    password: string;
  };

  type officialAccountConfirmOfficialAccountParams = {
    appid: string;
  };

  type OfficialAccountCreate = {
    /** Appid */
    appid: string;
    /** Category Id */
    category_id?: string | null;
  };

  type officialAccountDeleteOfficialAccountParams = {
    appid: string;
  };

  type OfficialAccountPublic = {
    /** Account Status */
    account_status?: number | null;
    /** App Type */
    app_type?: number | null;
    /** Appid */
    appid?: string | null;
    /** Auth Time */
    auth_time?: string | null;
    /** Basic Config */
    basic_config?: string | null;
    /** Func Info */
    func_info?: string | null;
    /** Head Img */
    head_img?: string | null;
    /** Nick Name */
    nick_name?: string | null;
    /** Principal Name */
    principal_name?: string | null;
    /** Qrcode Url */
    qrcode_url?: string | null;
    /** Refresh Token */
    refresh_token?: string | null;
    /** Register Type */
    register_type?: number | null;
    /** Service Type */
    service_type?: number | null;
    /** User Name */
    user_name?: string | null;
    /** Verify Info */
    verify_info?: number | null;
    /** Id */
    id: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Is Active */
    is_active?: boolean | null;
    category?: ArticleCategoryTable | null;
  };

  type OfficialAccountTable = {
    /** Account Status */
    account_status?: number | null;
    /** App Type */
    app_type?: number | null;
    /** Appid */
    appid?: string | null;
    /** Auth Time */
    auth_time?: string | null;
    /** Basic Config */
    basic_config?: string | null;
    /** Func Info */
    func_info?: string | null;
    /** Head Img */
    head_img?: string | null;
    /** Nick Name */
    nick_name?: string | null;
    /** Principal Name */
    principal_name?: string | null;
    /** Qrcode Url */
    qrcode_url?: string | null;
    /** Refresh Token */
    refresh_token?: string | null;
    /** Register Type */
    register_type?: number | null;
    /** Service Type */
    service_type?: number | null;
    /** User Name */
    user_name?: string | null;
    /** Verify Info */
    verify_info?: number | null;
    /** Id */
    id: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Is Active */
    is_active?: boolean;
    /** User Id */
    user_id?: string | null;
    /** Category Id */
    category_id?: string | null;
  };

  type PagePoeSessionPublic_ = {
    /** Items */
    items: PoeSessionPublic[];
    /** Total */
    total: number | null;
    /** Page */
    page: number | null;
    /** Size */
    size: number | null;
    /** Pages */
    pages?: number | null;
  };

  type PageTypeVarCustomizedArticlePublic_ = {
    /** Items */
    items: ArticlePublic[];
    /** Total */
    total: number | null;
    /** Page */
    page: number | null;
    /** Size */
    size: number | null;
    /** Pages */
    pages?: number | null;
  };

  type PageTypeVarCustomizedArticleSummaryInfo_ = {
    /** Items */
    items: ArticleSummaryInfo[];
    /** Total */
    total: number | null;
    /** Page */
    page: number | null;
    /** Size */
    size: number | null;
    /** Pages */
    pages?: number | null;
  };

  type PageTypeVarCustomizedArticleThumbTable_ = {
    /** Items */
    items: ArticleThumbTable[];
    /** Total */
    total: number | null;
    /** Page */
    page: number | null;
    /** Size */
    size: number | null;
    /** Pages */
    pages?: number | null;
  };

  type PageTypeVarCustomizedPromoteTablePublic_ = {
    /** Items */
    items: PromoteTablePublic[];
    /** Total */
    total: number | null;
    /** Page */
    page: number | null;
    /** Size */
    size: number | null;
    /** Pages */
    pages?: number | null;
  };

  type PageTypeVarCustomizedThirdApiKeyPublic_ = {
    /** Items */
    items: ThirdApiKeyPublic[];
    /** Total */
    total: number | null;
    /** Page */
    page: number | null;
    /** Size */
    size: number | null;
    /** Pages */
    pages?: number | null;
  };

  type PoeClientCreate = {
    /** Name */
    name: string;
    /** Token P B */
    token_p_b: string;
    /** Token P Lat */
    token_p_lat: string;
  };

  type PoeClientPublic = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Name */
    name: string;
    /** Token P B */
    token_p_b: string;
    /** Token P Lat */
    token_p_lat: string;
    /** Is Active */
    is_active: boolean;
  };

  type PoeClientTable = {
    /** Name */
    name: string;
    /** Token P B */
    token_p_b: string;
    /** Token P Lat */
    token_p_lat: string;
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Is Active */
    is_active?: boolean | null;
    /** User Id */
    user_id: string;
  };

  type PoeDefaultSetting = {
    /** Generate Image */
    generate_image?: boolean | null;
    generate_image_platform?: ImagePlatform | null;
    /** Generate Image Nums */
    generate_image_nums?: number | null;
    /** Article Client Id */
    article_client_id?: string | null;
    /** Article Session Id */
    article_session_id?: string | null;
    /** Article Promote */
    article_promote?: string | null;
    /** Article Ref Platform */
    article_ref_platform?: string | null;
    /** Article Ref Num */
    article_ref_num?: number | null;
    /** Article Generate Outline First */
    article_generate_outline_first?: boolean | null;
    /** Article Bot */
    article_bot?: string | null;
    /** Outline Client Id */
    outline_client_id?: string | null;
    /** Outline Session Id */
    outline_session_id?: string | null;
    /** Outline Promote */
    outline_promote?: string | null;
    /** Outline Bot */
    outline_bot?: string | null;
    /** Id */
    id: string;
  };

  type PoeDefaultSettingIn = {
    /** Generate Image */
    generate_image?: boolean | null;
    generate_image_platform?: ImagePlatform | null;
    /** Generate Image Nums */
    generate_image_nums?: number | null;
    /** Article Client Id */
    article_client_id?: string | null;
    /** Article Session Id */
    article_session_id?: string | null;
    /** Article Promote */
    article_promote?: string | null;
    /** Article Ref Platform */
    article_ref_platform?: string | null;
    /** Article Ref Num */
    article_ref_num?: number | null;
    /** Article Generate Outline First */
    article_generate_outline_first?: boolean | null;
    /** Article Bot */
    article_bot?: string | null;
    /** Outline Client Id */
    outline_client_id?: string | null;
    /** Outline Session Id */
    outline_session_id?: string | null;
    /** Outline Promote */
    outline_promote?: string | null;
    /** Outline Bot */
    outline_bot?: string | null;
  };

  type poeDeletePoeClientParams = {
    client_id: string;
  };

  type poeDeletePoeSessionParams = {
    session_id: string;
  };

  type poeGetPoeChatSessionParams = {
    session_id: string;
  };

  type poeGetPoeChatSessionsParams = {
    client_id?: string;
    is_active?: boolean;
    startTime?: string;
    endTime?: string;
    page?: number;
    size?: number;
  };

  type poeGetPoeClientListParams = {
    is_active?: boolean;
    startTime?: string;
    endTime?: string;
  };

  type PoeSessionCreate = {
    /** Name */
    name: string;
    /** Bot */
    bot: string;
    /** Client Id */
    client_id: string;
  };

  type PoeSessionPublic = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Name */
    name: string;
    /** Bot */
    bot: string;
    /** Chat Id */
    chat_id: number | null;
    /** Chat Code */
    chat_code: string | null;
    /** Is Active */
    is_active: boolean | null;
    /** Succeed */
    succeed: boolean | null;
    /** Exception */
    exception: boolean | null;
    /** Display */
    display: boolean | null;
    client: PoeClientTable;
  };

  type promoteDeletePromoteParams = {
    id: string;
  };

  type promoteGetAllPromotesParams = {
    is_active?: boolean | null;
    category_id?: string | null;
    startTime?: string;
    endTime?: string;
    current?: number;
    pageSize?: number;
  };

  type promoteGetPromoteByIdParams = {
    id: string;
  };

  type PromoteTableCreate = {
    /** Name */
    name: string;
    /** Promote */
    promote?: string;
    /** Category Id */
    category_id?: string | null;
  };

  type PromoteTablePublic = {
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Name */
    name: string;
    /** Promote */
    promote?: string;
    /** Category Id */
    category_id?: string | null;
    /** Is Active */
    is_active: boolean | null;
    category?: ArticleCategoryTable | null;
  };

  type PromoteTableUpdate = {
    /** Name */
    name?: string | null;
    /** Promote */
    promote?: string | null;
    /** Category Id */
    category_id?: string | null;
  };

  type promoteUpdatePromoteParams = {
    id: string;
  };

  type qiniuGetQiniuUploadTokenParams = {
    bucket_name?: string;
  };

  type ResponseBodyApiDefaultSetting_ = {
    /** Succeed */
    succeed: boolean;
    data?: ApiDefaultSetting | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyArticleCategoryPublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: ArticleCategoryPublic | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyArticlePublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: ArticlePublic | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListArticleCategoryPublic_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: ArticleCategoryPublic[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListArticlePublic_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: ArticlePublic[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListImagePlatformOption_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: ImagePlatformOption[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListOfficialAccountPublic_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: OfficialAccountPublic[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListPoeClientPublic_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: PoeClientPublic[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListStr_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: string[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListThirdApiKeyPlatformOption_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: ThirdApiKeyPlatformOption[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPagePoeSessionPublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PagePoeSessionPublic_ | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPageTypeVarCustomizedArticlePublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PageTypeVarCustomizedArticlePublic_ | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPageTypeVarCustomizedArticleSummaryInfo_ = {
    /** Succeed */
    succeed: boolean;
    data?: PageTypeVarCustomizedArticleSummaryInfo_ | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPageTypeVarCustomizedArticleThumbTable_ = {
    /** Succeed */
    succeed: boolean;
    data?: PageTypeVarCustomizedArticleThumbTable_ | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPageTypeVarCustomizedPromoteTablePublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PageTypeVarCustomizedPromoteTablePublic_ | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPageTypeVarCustomizedThirdApiKeyPublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PageTypeVarCustomizedThirdApiKeyPublic_ | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPoeClientPublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PoeClientPublic | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPoeDefaultSetting_ = {
    /** Succeed */
    succeed: boolean;
    data?: PoeDefaultSetting | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPoeSessionPublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PoeSessionPublic | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyPromoteTablePublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: PromoteTablePublic | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyStr_ = {
    /** Succeed */
    succeed: boolean;
    /** Data */
    data?: string | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyThirdApiKeyPublic_ = {
    /** Succeed */
    succeed: boolean;
    data?: ThirdApiKeyPublic | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type SyncArticleToDraft = {
    /** Article Id */
    article_id: string[];
    /** Official Account Id */
    official_account_id: string;
    /** Author */
    author: string;
    /** Thumb Media Id */
    thumb_media_id?: string | null;
    /** Need Open Comment */
    need_open_comment?: boolean;
    /** Only Fans Can Comment */
    only_fans_can_comment?: boolean;
    /** Num Of Group */
    num_of_group?: number;
  };

  type TaskStatusEnum = '生成中' | '完成' | '异常';

  type ThirdApiKeyCreate = {
    /** Name */
    name: string;
    platform: ThirdApiPlatform;
    /** Api Key */
    api_key: string;
  };

  type thirdApiKeyDeleteThirdApiKeyParams = {
    id: string;
  };

  type thirdApiKeyGetAllThirdApiKeyParams = {
    platform?: ThirdApiPlatform | null;
    is_active?: boolean | null;
    startTime?: string;
    endTime?: string;
    current?: number;
    pageSize?: number;
  };

  type thirdApiKeyGetModelsParams = {
    platform: ThirdApiPlatform;
  };

  type thirdApiKeyGetThirdApiKeyByIdParams = {
    id: string;
  };

  type ThirdApiKeyPlatformOption = {
    /** Label */
    label: string;
    /** Value */
    value: string;
  };

  type ThirdApiKeyPublic = {
    /** Name */
    name: string;
    platform: ThirdApiPlatform;
    /** Api Key */
    api_key: string;
    /** Id */
    id?: string;
    /** Create At */
    create_at?: string;
    /** Update At */
    update_at?: string;
    /** Deleted At */
    deleted_at?: string | null;
    /** Is Delete */
    is_delete?: boolean;
    /** Is Active */
    is_active: boolean | null;
  };

  type ThirdApiKeyUpdate = {
    /** Name */
    name?: string | null;
    /** Api Key */
    api_key?: string | null;
  };

  type thirdApiKeyUpdateThirdApiKeyParams = {
    id: string;
  };

  type ThirdApiPlatform = '百度千帆' | '腾讯DeepSeek(offline)' | 'POE' | 'DeepSeek(official)';

  type Token = {
    /** Status */
    status: boolean;
    /** Access Token */
    access_token: string;
    /** Token Type */
    token_type?: string;
  };

  // 微信组件相关类型定义
  type WxPreAuthCodeResponse = {
    /** 预授权码 */
    pre_auth_code: string;
    /** 过期时间（秒） */
    expires_in: number;
  };

  type WxAuthUrlRequest = {
    /** 用户ID */
    user_id: string;
    /** 公众号AppID（可选，指定授权特定公众号） */
    appid?: string;
    /** 授权类型：1-公众号，2-小程序，3-公众号和小程序，6-全部 */
    auth_type: number;
    /** 权限集ID列表（可选） */
    category_id_list?: string;
    /** 授权回调URI */
    redirect_uri: string;
  };

  type WxAuthUrlResponse = {
    /** 授权链接 */
    auth_url: string;
    /** 二维码链接 */
    qr_code_url: string;
    /** 预授权码 */
    pre_auth_code: string;
    /** 授权会话ID */
    auth_session_id: string;
  };

  type WxAuthCallbackRequest = {
    /** 授权码 */
    auth_code: string;
    /** 过期时间 */
    expires_in: number;
    /** 授权会话ID */
    auth_session_id: string;
  };

  type WxAuthCallbackResponse = {
    /** 授权成功 */
    success: boolean;
    /** 授权的公众号信息 */
    authorizer_info?: API.OfficialAccountPublic;
  };

  type WxAuthStatusResponse = {
    /** 授权状态：pending-等待授权，authorized-已授权，expired-已过期 */
    status: 'pending' | 'authorized' | 'expired';
    /** 授权的公众号信息（如果已授权） */
    authorizer_info?: API.OfficialAccountPublic;
    /** 授权时间 */
    auth_time?: string;
  };

  type WxUserAuthRecord = {
    /** ID */
    id: string;
    /** 用户ID */
    user_id: string;
    /** 公众号AppID */
    appid: string;
    /** 公众号名称 */
    nick_name: string;
    /** 授权时间 */
    auth_time: string;
    /** 授权状态 */
    status: 'active' | 'inactive';
    /** 授权会话ID */
    auth_session_id: string;
  };

  // API参数类型
  type wxComponentGetPreAuthCodeParams = {
    /** 用户ID */
    user_id: string;
  };

  type wxComponentGetAuthStatusParams = {
    /** 授权会话ID */
    auth_session_id: string;
  };

  type wxComponentGetUserAuthRecordsParams = {
    /** 用户ID */
    user_id?: string;
    /** 页码 */
    current?: string;
    /** 页大小 */
    pageSize?: string;
  };

  // 响应体类型
  type ResponseBodyWxPreAuthCodeResponse_ = {
    /** Succeed */
    succeed: boolean;
    data?: WxPreAuthCodeResponse | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyWxAuthUrlResponse_ = {
    /** Succeed */
    succeed: boolean;
    data?: WxAuthUrlResponse | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyWxAuthStatusResponse_ = {
    /** Succeed */
    succeed: boolean;
    data?: WxAuthStatusResponse | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyWxAuthCallbackResponse_ = {
    /** Succeed */
    succeed: boolean;
    data?: WxAuthCallbackResponse | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type ResponseBodyListWxUserAuthRecord_ = {
    /** Succeed */
    succeed: boolean;
    data?: WxUserAuthRecord[] | null;
    /** Errorcode */
    errorCode?: number | null;
    /** Errormessage */
    errorMessage?: string | null;
  };

  type UserPublic = {
    /** Username */
    username: string;
    /** Is Admin */
    is_admin?: boolean;
    /** Is Active */
    is_active?: boolean;
    /** Avatar */
    avatar: string;
    /** Id */
    id: string;
  };

  type ValidationError = {
    /** Location */
    loc: (string | number)[];
    /** Message */
    msg: string;
    /** Error Type */
    type: string;
  };
}
